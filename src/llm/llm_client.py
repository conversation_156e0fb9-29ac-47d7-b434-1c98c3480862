from langchain.chat_models import init_chat_model
from langchain_core.language_models import BaseChatModel

from src.config import appConfig


def get_llm(**kwargs) -> BaseChatModel:
    """获取 LLM 客户端"""
    return init_chat_model(
        model_provider=appConfig.llm.provider,
        model=appConfig.llm.model_name,
        api_key=appConfig.llm.api_key,
        base_url=appConfig.llm.base_url,
        temperature=appConfig.llm.temperature,
        **kwargs,
    )
