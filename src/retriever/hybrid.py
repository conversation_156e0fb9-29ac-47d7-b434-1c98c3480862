from langchain.retrievers import EnsembleRetriever
from langchain_community.retrievers import BM25Retriever
from langchain_core.documents import Document
from langchain_core.retrievers import BaseRetriever


def build_bm25_retriever(docs: list[Document]) -> BM25Retriever:
    """基于文档构建 BM25 检索器"""
    return BM25Retriever.from_documents(docs)


def get_hybrid_retriever(
    retrievers: list[BaseRetriever], weights: list[float]
) -> EnsembleRetriever:
    """获取混合检索器"""
    return EnsembleRetriever(retrievers=retrievers, weights=weights)
