from langchain_core.documents import Document
from sentence_transformers import CrossEncoder


class Reranker:
    """重排序器"""

    def __init__(self, model_name: str):
        self.model = CrossEncoder(model_name)

    def rerank(
        self, query: str, docs: list[Document], top_k: int = 5
    ) -> list[Document]:
        """重排序"""
        pairs = [(query, doc.page_content) for doc in docs]
        scores = self.model.predict(pairs)
        scored_docs = list(zip(docs, scores))
        scored_docs.sort(key=lambda x: x[1], reverse=True)
        return [doc for doc, _ in scored_docs[:top_k]]
