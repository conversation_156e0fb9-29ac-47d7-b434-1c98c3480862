from typing import List, Optional, Any

from langchain.retrievers import EnsembleRetriever
from langchain_core.documents import Document
from langchain_core.retrievers import BaseRetriever
from langchain_core.callbacks import CallbackManagerForRetrieverRun
from sentence_transformers import CrossEncoder


class RetrieverPipeline(BaseRetriever):
    """
    通用检索管道：
    - 支持多个 retriever 融合
    - 支持 CrossEncoder reranker 精排
    """
    retrievers: List[BaseRetriever]
    weights: Optional[List[float]] = None
    reranker_model: Optional[str] = None
    rerank_top_k: int = 5
    ensemble: Optional[EnsembleRetriever] = None
    reranker: Optional[Any] = None

    def __init__(self, **data):
        super().__init__(**data)

        # 初始化 ensemble retriever
        self.ensemble = EnsembleRetriever(
            retrievers=self.retrievers, weights=self.weights
        )

        # 初始化 reranker
        if self.reranker_model:
            try:
                self.reranker = CrossEncoder(self.reranker_model)
            except Exception as e:
                print(f"Warning: Failed to load reranker model {self.reranker_model}: {e}")
                self.reranker = None
        else:
            self.reranker = None

    def _invoke(
            self,
            query: str,
            run_manager: Optional[CallbackManagerForRetrieverRun] = None,
    ) -> List[Document]:
        try:
            # 多检索器融合召回
            candidates = self.ensemble.invoke(query, config={"run_manager": run_manager})

            # reranker 精排
            if self.reranker and candidates:
                try:
                    pairs = [(query, doc.page_content) for doc in candidates]
                    scores = self.reranker.predict(pairs)
                    scored_docs = list(zip(candidates, scores))
                    scored_docs.sort(key=lambda x: x[1], reverse=True)
                    return [doc for doc, _ in scored_docs[: self.rerank_top_k]]
                except Exception as e:
                    print(f"Warning: Reranking failed: {e}, returning original candidates")
                    return candidates

            return candidates
        except Exception as e:
            print(f"Error in retrieval: {e}")
            return []

    # 为了向后兼容，保留旧方法但标记为弃用
    def _get_relevant_documents(self, query: str) -> List[Document]:
        """已弃用：使用 _invoke 替代"""
        return self._invoke(query)
