from pymilvus import MilvusClient

from src.vector.milvus_client import get_milvus_client


class CollectionManager:
    """向量集合管理器"""

    def __init__(self, client: MilvusClient | None = None):
        self.client = client or get_milvus_client()

    def create_collection(
        self, collection_name: str, dimension: int, metric_type: str = "IP"
    ):
        """创建向量集合"""
        if not self.client.has_collection(collection_name):
            self.client.create_collection(collection_name, dimension, metric_type)

    def drop_collection(self, collection_name: str):
        """删除向量集合"""
        if self.client.has_collection(collection_name):
            self.client.drop_collection(collection_name)

    def list_collections(self):
        """列出所有向量集合"""
        return self.client.list_collections()


# 全局集合管理器实例
collection_manager = CollectionManager()
