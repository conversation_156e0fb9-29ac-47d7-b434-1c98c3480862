import os

from pydantic import BaseModel, Field

from .embedding_config import EmbeddingConfig
from .langsmith_config import LangSmithConfig
from .llm_config import LLMConfig
from .retriever_config import RetrieverConfig
from .vector_config import VectorConfig


class AppConfig(BaseModel):
    """
    应用配置
    """

    llm: LLMConfig = Field(default_factory=LLMConfig)
    embedding: EmbeddingConfig = Field(default_factory=EmbeddingConfig)
    vector: VectorConfig = Field(default_factory=VectorConfig)
    retriever: RetrieverConfig = Field(default_factory=RetrieverConfig)
    langsmith: LangSmithConfig = Field(default_factory=LangSmithConfig)


# 全局配置实例
appConfig = AppConfig()

# 设置不需要 Auth 校验
os.environ["DANGEROUSLY_OMIT_AUTH"] = "true"

# 设置 LangSmith 环境变量
if appConfig.langsmith.tracing:
    os.environ["LANGCHAIN_TRACING"] = "true"
    os.environ["LANGCHAIN_API_KEY"] = appConfig.langsmith.api_key
    os.environ["LANGCHAIN_PROJECT"] = appConfig.langsmith.project
