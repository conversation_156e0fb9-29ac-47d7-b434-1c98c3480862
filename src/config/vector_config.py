from typing import Optional

from pydantic import BaseModel, Field

from .base import TOML_CONFIG


class VectorConfig(BaseModel):
    """向量数据库配置"""

    uri: str = Field(
        default=TOML_CONFIG.get("vector", {}).get("uri", "http://localhost:19530"),
        description="Milvus 服务地址",
    )

    user: str = Field(
        default=TOML_CONFIG.get("vector", {}).get("user", ""),
        description="用户名（若启用认证）",
    )

    password: str = Field(
        default=TOML_CONFIG.get("vector", {}).get("password", ""),
        description="用户密码（若启用认证）",
    )

    db_name: str = Field(
        default=TOML_CONFIG.get("vector", {}).get("db_name", ""),
        description="目标数据库名称（默认 default）",
    )

    token: str = Field(
        default=TOML_CONFIG.get("vector", {}).get("token", ""),
        description="身份认证 Token，可以替代 user/password",
    )

    timeout: Optional[float] = Field(
        default=TOML_CONFIG.get("vector", {}).get("timeout", None),
        description="连接或请求超时时间（秒）",
    )
