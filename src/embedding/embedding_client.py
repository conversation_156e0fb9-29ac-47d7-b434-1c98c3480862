from pathlib import Path
from typing import Any, Union

from langchain.embeddings import init_embeddings
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_core.embeddings import Embeddings
from langchain_core.runnables import Runnable

from src.config import appConfig


def get_embedding() -> Union[Embeddings, Runnable[Any, list[float]]]:
    """获取 Embedding 客户端"""

    model_name = appConfig.embedding.model_name

    # 如果是相对路径，自动转成绝对路径
    model_path = Path(model_name)
    if not model_path.is_absolute():
        model_path = (Path(__file__).resolve().parents[2] / model_path).resolve()

    # 如果路径存在，说明是本地模型
    if model_path.exists():
        return HuggingFaceEmbeddings(
            model_name=str(model_path),
            model_kwargs={"device": appConfig.embedding.device},
            encode_kwargs={"normalize_embeddings": True},
        )

    # 从 Hugging Face Hub 下载模型
    return init_embeddings(
        provider=appConfig.embedding.provider,
        model=model_name,
    )
