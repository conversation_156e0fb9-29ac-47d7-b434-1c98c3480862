.PHONY: install dev clean lint format test

# 安装依赖
install:
	uv sync

# 安装开发依赖
dev:
	uv sync
	uv sync --extra dev

# 清理项目
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

# 代码检查
lint:
	uv run ruff check .
	uv run black --check .
	uv run isort --check .

# 代码格式化
format:
	uv run ruff check --fix .
	uv run black .
	uv run isort .

# 运行测试
test:
	uv run pytest
