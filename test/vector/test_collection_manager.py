import unittest

from src.vector.collection_manager import collection_manager


class TestCollectionManager(unittest.TestCase):
    """测试 CollectionManager"""

    def setUp(self):
        self.test_collection_name = "test_collection_manager_collection"
        self.test_dim = 1024
        self.test_metric_type = "IP"

    def test_create_collection(self):
        """测试创建集合"""
        collection_manager.create_collection(self.test_collection_name, self.test_dim, self.test_metric_type)
        collections = collection_manager.list_collections()
        print(collections)
        assert self.test_collection_name in collections

    def test_drop_collection(self):
        """测试删除集合"""
        collection_manager.drop_collection(self.test_collection_name)
        collections = collection_manager.list_collections()
        print(collections)
        assert self.test_collection_name not in collections
